#!/usr/bin/env python
"""
Demo script showing how to add new model configuration without hardcoding.
"""

import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'x_erp.settings')
django.setup()

from django_ledger.api.decorators import (
    add_model_config,
    get_model_error_config,
    get_model_error_configs
)

def demo_add_new_models():
    """Demo adding multiple new model configurations."""
    print("🎯 Demo: Adding New Model Configurations")
    print("=" * 60)
    
    # Example: Adding Product Category model
    print("\n📦 Adding ProductCategoryViewSet configuration...")
    product_category_config = {
        'unique_constraints': {
            'ma_dm': {
                'message': 'Product category code already exists',
                'error_code': 'DUPLICATE_CATEGORY_CODE'
            },
            'ten_dm': {
                'message': 'Product category name already exists',
                'error_code': 'DUPLICATE_CATEGORY_NAME'
            },
            'slug': {
                'message': 'Category slug already exists',
                'error_code': 'DUPLICATE_CATEGORY_SLUG'
            }
        },
        'model_name': 'ProductCategory',
        'category': 'product'
    }
    
    add_model_config("ProductCategoryViewSet", product_category_config)
    print("✅ ProductCategoryViewSet configuration added")
    
    # Example: Adding Unit of Measure model
    print("\n📏 Adding UnitOfMeasureViewSet configuration...")
    uom_config = {
        'unique_constraints': {
            'ma_dvt': {
                'message': 'Unit of measure code already exists',
                'error_code': 'DUPLICATE_UOM_CODE'
            },
            'ten_dvt': {
                'message': 'Unit of measure name already exists',
                'error_code': 'DUPLICATE_UOM_NAME'
            },
            'symbol': {
                'message': 'Unit symbol already exists',
                'error_code': 'DUPLICATE_UOM_SYMBOL'
            }
        },
        'model_name': 'UnitOfMeasure',
        'category': 'measurement'
    }
    
    add_model_config("UnitOfMeasureViewSet", uom_config)
    print("✅ UnitOfMeasureViewSet configuration added")
    
    # Example: Adding Tax Rate model
    print("\n💰 Adding TaxRateViewSet configuration...")
    tax_config = {
        'unique_constraints': {
            'ma_thue': {
                'message': 'Tax code already exists',
                'error_code': 'DUPLICATE_TAX_CODE'
            },
            'ten_thue': {
                'message': 'Tax name already exists',
                'error_code': 'DUPLICATE_TAX_NAME'
            }
        },
        'model_name': 'TaxRate',
        'category': 'tax'
    }
    
    add_model_config("TaxRateViewSet", tax_config)
    print("✅ TaxRateViewSet configuration added")
    
    # Example: Adding Bank Account model
    print("\n🏦 Adding BankAccountViewSet configuration...")
    bank_config = {
        'unique_constraints': {
            'so_tk': {
                'message': 'Bank account number already exists',
                'error_code': 'DUPLICATE_BANK_ACCOUNT'
            },
            'iban': {
                'message': 'IBAN already exists',
                'error_code': 'DUPLICATE_IBAN'
            }
        },
        'model_name': 'BankAccount',
        'category': 'banking'
    }
    
    add_model_config("BankAccountViewSet", bank_config)
    print("✅ BankAccountViewSet configuration added")
    
    # Example: Adding Project model
    print("\n🚀 Adding ProjectViewSet configuration...")
    project_config = {
        'unique_constraints': {
            'ma_da': {
                'message': 'Project code already exists',
                'error_code': 'DUPLICATE_PROJECT_CODE'
            },
            'ten_da': {
                'message': 'Project name already exists',
                'error_code': 'DUPLICATE_PROJECT_NAME'
            }
        },
        'model_name': 'Project',
        'category': 'project'
    }
    
    add_model_config("ProjectViewSet", project_config)
    print("✅ ProjectViewSet configuration added")
    
    print(f"\n🎉 Successfully added 5 new model configurations!")


def demo_verify_configurations():
    """Verify that configurations were added correctly."""
    print("\n🔍 Verifying Added Configurations")
    print("=" * 60)
    
    new_models = [
        "ProductCategoryViewSet",
        "UnitOfMeasureViewSet", 
        "TaxRateViewSet",
        "BankAccountViewSet",
        "ProjectViewSet"
    ]
    
    for model_name in new_models:
        config = get_model_error_config(model_name)
        model_display = config.get('model_name', 'Unknown')
        category = config.get('category', 'unknown')
        constraint_count = len(config.get('unique_constraints', {}))
        
        print(f"✅ {model_name}:")
        print(f"   Model: {model_display}")
        print(f"   Category: {category}")
        print(f"   Constraints: {constraint_count}")
        
        # Show constraint details
        for field, constraint in config.get('unique_constraints', {}).items():
            print(f"   - {field}: {constraint['error_code']}")


def demo_show_all_configurations():
    """Show all available configurations."""
    print("\n📋 All Available Model Configurations")
    print("=" * 60)
    
    all_configs = get_model_error_configs()
    
    # Group by category
    categories = {}
    for view_name, config in all_configs.items():
        category = config.get('category', 'unknown')
        if category not in categories:
            categories[category] = []
        categories[category].append({
            'view_name': view_name,
            'model_name': config.get('model_name', 'Unknown'),
            'constraint_count': len(config.get('unique_constraints', {}))
        })
    
    for category, models in categories.items():
        print(f"\n📂 {category.upper()} ({len(models)} models):")
        for model in models:
            print(f"   - {model['view_name']}: {model['model_name']} ({model['constraint_count']} constraints)")
    
    print(f"\n📊 Total: {len(all_configs)} model configurations")


def demo_error_simulation():
    """Simulate how errors would be handled with new configurations."""
    print("\n🧪 Error Handling Simulation")
    print("=" * 60)
    
    from django_ledger.api.decorators.error_handling import parse_integrity_error
    
    # Test scenarios
    test_cases = [
        {
            'view_name': 'ProductCategoryViewSet',
            'error': 'UNIQUE constraint failed: ma_dm',
            'expected_code': 'DUPLICATE_CATEGORY_CODE'
        },
        {
            'view_name': 'UnitOfMeasureViewSet', 
            'error': 'UNIQUE constraint failed: symbol',
            'expected_code': 'DUPLICATE_UOM_SYMBOL'
        },
        {
            'view_name': 'BankAccountViewSet',
            'error': 'UNIQUE constraint failed: iban',
            'expected_code': 'DUPLICATE_IBAN'
        },
        {
            'view_name': 'UnknownViewSet',
            'error': 'UNIQUE constraint failed: some_field',
            'expected_code': 'DUPLICATE_RESOURCE'
        }
    ]
    
    for test in test_cases:
        message, error_code = parse_integrity_error(test['error'], test['view_name'])
        
        print(f"\n🔧 {test['view_name']}:")
        print(f"   Error: {test['error']}")
        print(f"   Message: {message}")
        print(f"   Code: {error_code}")
        
        if error_code == test['expected_code']:
            print("   ✅ Correct error code detected")
        else:
            print(f"   ❌ Expected {test['expected_code']}, got {error_code}")


if __name__ == "__main__":
    print("🎯 Configuration-Driven Error Handling Demo")
    print("=" * 60)
    print("This demo shows how to add 100+ models without hardcoded if-else chains!")
    
    try:
        demo_add_new_models()
        demo_verify_configurations()
        demo_show_all_configurations()
        demo_error_simulation()
        
        print("\n" + "=" * 60)
        print("🏁 Demo completed successfully!")
        print("=" * 60)
        print("\n💡 Key Benefits:")
        print("   ✅ No hardcoded if-else chains")
        print("   ✅ Easy to add new models")
        print("   ✅ Consistent error handling")
        print("   ✅ Configuration-driven approach")
        print("   ✅ Scalable for 100+ models")
        print("   ✅ Maintainable and flexible")
        
    except Exception as e:
        print(f"\n💥 Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
