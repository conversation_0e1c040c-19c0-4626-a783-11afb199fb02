"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Serializer for LyDoTangGiamTaiSanCoDinh model.
"""

from django.utils.translation import gettext_lazy as _  # noqa: F401
from rest_framework import serializers  # noqa: F401

from django_ledger.api.serializers.entity import (  # noqa: F401
    EntityModelSerializer,
)
from django_ledger.models.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh import (  # noqa: F401,
    LyDoTangGiamTaiSanCoDinhModel,
)


class LyDoTangGiamTaiSanCoDinhSerializer(serializers.ModelSerializer):
    """
    Serializer for LyDoTangGiamTaiSanCoDinhModel.
    """

    class Meta:
        model = LyDoTangGiamTaiSanCoDinhModel
        fields = [
            'uuid',
            'entity_model',
            'loai_tg_ts',
            'ma_tg_ts',
            'ten_tg_ts',
            'ten_tg_ts2',
            'status',
            'created_by',
            'updated_by',
            'created',
            'updated',
        ]
        read_only_fields = ['uuid', 'entity_model', 'created', 'updated']
