"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Error handling configuration for API decorators.
"""

import json
import os
from typing import Any, Dict, Optional
from django.conf import settings


# Default configuration embedded in code
DEFAULT_MODEL_ERROR_CONFIGS = {
    # Department models
    'BoPhanSuDungCCDCViewSet': {
        'unique_constraints': {
            'ma_bp_sd': {
                'message': 'Department code already exists',
                'error_code': 'DUPLICATE_DEPARTMENT_CODE'
            },
            'ten_bp_sd': {
                'message': 'Department name already exists', 
                'error_code': 'DUPLICATE_DEPARTMENT_NAME'
            }
        },
        'model_name': 'BoPhanSuDungCCDC',
        'category': 'department'
    },
    'BoPhanSuDungTSViewSet': {
        'unique_constraints': {
            'ma_bp_sd': {
                'message': 'Department code already exists',
                'error_code': 'DUPLICATE_DEPARTMENT_CODE'
            },
            'ten_bp_sd': {
                'message': 'Department name already exists',
                'error_code': 'DUPLICATE_DEPARTMENT_NAME'
            }
        },
        'model_name': 'BoPhanSuDungTS',
        'category': 'department'
    },
    
    # Customer models
    'CustomerViewSet': {
        'unique_constraints': {
            'ma_kh': {
                'message': 'Customer code already exists',
                'error_code': 'DUPLICATE_CUSTOMER_CODE'
            },
            'email': {
                'message': 'Email address already exists',
                'error_code': 'DUPLICATE_EMAIL'
            },
            'phone': {
                'message': 'Phone number already exists',
                'error_code': 'DUPLICATE_PHONE'
            }
        },
        'model_name': 'Customer',
        'category': 'customer'
    },
    
    # Vendor models
    'VendorViewSet': {
        'unique_constraints': {
            'ma_ncc': {
                'message': 'Vendor code already exists',
                'error_code': 'DUPLICATE_VENDOR_CODE'
            },
            'tax_id': {
                'message': 'Tax ID already exists',
                'error_code': 'DUPLICATE_TAX_ID'
            }
        },
        'model_name': 'Vendor',
        'category': 'vendor'
    },
    
    # Material models
    'VatTuViewSet': {
        'unique_constraints': {
            'ma_vt': {
                'message': 'Material code already exists',
                'error_code': 'DUPLICATE_MATERIAL_CODE'
            },
            'barcode': {
                'message': 'Barcode already exists',
                'error_code': 'DUPLICATE_BARCODE'
            },
            'sku': {
                'message': 'SKU already exists',
                'error_code': 'DUPLICATE_SKU'
            }
        },
        'model_name': 'VatTu',
        'category': 'material'
    },
    
    # Account models
    'AccountModelViewSet': {
        'unique_constraints': {
            'code': {
                'message': 'Account code already exists',
                'error_code': 'DUPLICATE_ACCOUNT_CODE'
            },
            'account_number': {
                'message': 'Account number already exists',
                'error_code': 'DUPLICATE_ACCOUNT_NUMBER'
            }
        },
        'model_name': 'Account',
        'category': 'accounting'
    },
    
    # Warehouse models
    'KhoHangViewSet': {
        'unique_constraints': {
            'ma_kho': {
                'message': 'Warehouse code already exists',
                'error_code': 'DUPLICATE_WAREHOUSE_CODE'
            },
            'name': {
                'message': 'Warehouse name already exists',
                'error_code': 'DUPLICATE_WAREHOUSE_NAME'
            }
        },
        'model_name': 'KhoHang',
        'category': 'warehouse'
    },
    
    # Employee models
    'NhanVienModelViewSet': {
        'unique_constraints': {
            'ma_nv': {
                'message': 'Employee code already exists',
                'error_code': 'DUPLICATE_EMPLOYEE_CODE'
            },
            'email': {
                'message': 'Employee email already exists',
                'error_code': 'DUPLICATE_EMPLOYEE_EMAIL'
            },
            'citizen_id': {
                'message': 'Citizen ID already exists',
                'error_code': 'DUPLICATE_CITIZEN_ID'
            }
        },
        'model_name': 'NhanVien',
        'category': 'employee'
    },
    
    # Invoice models
    'InvoiceViewSet': {
        'unique_constraints': {
            'invoice_number': {
                'message': 'Invoice number already exists',
                'error_code': 'DUPLICATE_INVOICE_NUMBER'
            }
        },
        'model_name': 'Invoice',
        'category': 'document'
    },
    
    # Bill models
    'BillViewSet': {
        'unique_constraints': {
            'bill_number': {
                'message': 'Bill number already exists',
                'error_code': 'DUPLICATE_BILL_NUMBER'
            }
        },
        'model_name': 'Bill',
        'category': 'document'
    }
}


def load_external_config() -> Optional[Dict[str, Any]]:
    """
    Load error configuration from external JSON file.
    
    Returns
    -------
    Optional[Dict[str, Any]]
        External configuration if file exists and is valid, None otherwise
    """
    try:
        # Try to load from Django settings first
        config_path = getattr(settings, 'API_ERROR_CONFIG_PATH', None)
        
        if not config_path:
            # Default path in project root
            config_path = os.path.join(settings.BASE_DIR, 'api_error_config.json')
        
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
                
    except (FileNotFoundError, json.JSONDecodeError, AttributeError) as e:
        # Log warning but don't fail - fall back to default config
        import logging
        logger = logging.getLogger(__name__)
        logger.warning(f"Could not load external error config: {e}")
    
    return None


def get_model_error_configs() -> Dict[str, Any]:
    """
    Get complete model error configuration.
    
    Loads from external file if available, otherwise uses default configuration.
    External config can override or extend default config.
    
    Returns
    -------
    Dict[str, Any]
        Complete error configuration
    """
    # Start with default config
    config = DEFAULT_MODEL_ERROR_CONFIGS.copy()
    
    # Try to load and merge external config
    external_config = load_external_config()
    if external_config:
        # Merge external config with default
        for view_name, view_config in external_config.items():
            if view_name in config:
                # Merge unique_constraints
                if 'unique_constraints' in view_config:
                    config[view_name]['unique_constraints'].update(
                        view_config['unique_constraints']
                    )
                # Update other fields
                for key, value in view_config.items():
                    if key != 'unique_constraints':
                        config[view_name][key] = value
            else:
                # Add new view config
                config[view_name] = view_config
    
    return config


def get_model_error_config(view_class_name: str) -> Dict[str, Any]:
    """
    Get error configuration for a specific model/view.
    
    Parameters
    ----------
    view_class_name : str
        The name of the ViewSet class
        
    Returns
    -------
    Dict[str, Any]
        Error configuration for the model
    """
    all_configs = get_model_error_configs()
    return all_configs.get(view_class_name, {
        'unique_constraints': {},
        'model_name': 'Unknown',
        'category': 'unknown'
    })


def add_model_config(view_class_name: str, config: Dict[str, Any]) -> None:
    """
    Dynamically add or update model configuration at runtime.
    
    Parameters
    ----------
    view_class_name : str
        The name of the ViewSet class
    config : Dict[str, Any]
        Configuration for the model
    """
    # This would be used for dynamic configuration updates
    # Could be extended to save to external file
    DEFAULT_MODEL_ERROR_CONFIGS[view_class_name] = config


def generate_config_template() -> str:
    """
    Generate a JSON template for external configuration file.
    
    Returns
    -------
    str
        JSON template string
    """
    template = {
        "_comment": "API Error Handling Configuration",
        "_description": "Configure error messages and codes for different models",
        "YourModelViewSet": {
            "unique_constraints": {
                "field_name": {
                    "message": "Custom error message",
                    "error_code": "CUSTOM_ERROR_CODE"
                }
            },
            "model_name": "YourModel",
            "category": "your_category"
        }
    }
    
    return json.dumps(template, indent=2, ensure_ascii=False)
