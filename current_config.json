{"BoPhanSuDungCCDCViewSet": {"unique_constraints": {"ma_bp_sd": {"message": "Department code already exists", "error_code": "DUPLICATE_DEPARTMENT_CODE"}, "ten_bp_sd": {"message": "Department name already exists", "error_code": "DUPLICATE_DEPARTMENT_NAME"}}, "model_name": "BoPhanSuDungCCDC", "category": "department"}, "BoPhanSuDungTSViewSet": {"unique_constraints": {"ma_bp_sd": {"message": "Department code already exists", "error_code": "DUPLICATE_DEPARTMENT_CODE"}, "ten_bp_sd": {"message": "Department name already exists", "error_code": "DUPLICATE_DEPARTMENT_NAME"}}, "model_name": "BoPhanSuDungTS", "category": "department"}, "CustomerViewSet": {"unique_constraints": {"ma_kh": {"message": "Customer code already exists", "error_code": "DUPLICATE_CUSTOMER_CODE"}, "email": {"message": "Email address already exists", "error_code": "DUPLICATE_EMAIL"}, "phone": {"message": "Phone number already exists", "error_code": "DUPLICATE_PHONE"}}, "model_name": "Customer", "category": "customer"}, "VendorViewSet": {"unique_constraints": {"ma_ncc": {"message": "Vendor code already exists", "error_code": "DUPLICATE_VENDOR_CODE"}, "tax_id": {"message": "Tax ID already exists", "error_code": "DUPLICATE_TAX_ID"}}, "model_name": "<PERSON><PERSON><PERSON>", "category": "vendor"}, "VatTuViewSet": {"unique_constraints": {"ma_vt": {"message": "Material code already exists", "error_code": "DUPLICATE_MATERIAL_CODE"}, "barcode": {"message": "Barcode already exists", "error_code": "DUPLICATE_BARCODE"}, "sku": {"message": "SKU already exists", "error_code": "DUPLICATE_SKU"}}, "model_name": "VatTu", "category": "material"}, "AccountModelViewSet": {"unique_constraints": {"code": {"message": "Account code already exists", "error_code": "DUPLICATE_ACCOUNT_CODE"}, "account_number": {"message": "Account number already exists", "error_code": "DUPLICATE_ACCOUNT_NUMBER"}}, "model_name": "Account", "category": "accounting"}, "KhoHangViewSet": {"unique_constraints": {"ma_kho": {"message": "Warehouse code already exists", "error_code": "DUPLICATE_WAREHOUSE_CODE"}, "name": {"message": "Warehouse name already exists", "error_code": "DUPLICATE_WAREHOUSE_NAME"}}, "model_name": "<PERSON><PERSON><PERSON><PERSON>", "category": "warehouse"}, "NhanVienModelViewSet": {"unique_constraints": {"ma_nv": {"message": "Employee code already exists", "error_code": "DUPLICATE_EMPLOYEE_CODE"}, "email": {"message": "Employee email already exists", "error_code": "DUPLICATE_EMPLOYEE_EMAIL"}, "citizen_id": {"message": "Citizen ID already exists", "error_code": "DUPLICATE_CITIZEN_ID"}}, "model_name": "NhanVien", "category": "employee"}, "InvoiceViewSet": {"unique_constraints": {"invoice_number": {"message": "Invoice number already exists", "error_code": "DUPLICATE_INVOICE_NUMBER"}}, "model_name": "Invoice", "category": "document"}, "BillViewSet": {"unique_constraints": {"bill_number": {"message": "Bill number already exists", "error_code": "DUPLICATE_BILL_NUMBER"}}, "model_name": "Bill", "category": "document"}}