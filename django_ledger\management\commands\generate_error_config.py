"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Management command to generate API error configuration template.
"""

import json
import os
from django.core.management.base import BaseCommand
from django.conf import settings
from django_ledger.api.decorators.error_config import (
    generate_config_template,
    get_model_error_configs
)


class Command(BaseCommand):
    help = 'Generate API error configuration template or export current config'

    def add_arguments(self, parser):
        parser.add_argument(
            '--output',
            type=str,
            default='api_error_config.json',
            help='Output file path (default: api_error_config.json)'
        )
        parser.add_argument(
            '--template',
            action='store_true',
            help='Generate empty template instead of current config'
        )
        parser.add_argument(
            '--overwrite',
            action='store_true',
            help='Overwrite existing file'
        )

    def handle(self, *args, **options):
        output_path = options['output']
        
        # Make path absolute if relative
        if not os.path.isabs(output_path):
            output_path = os.path.join(settings.BASE_DIR, output_path)
        
        # Check if file exists
        if os.path.exists(output_path) and not options['overwrite']:
            self.stdout.write(
                self.style.ERROR(
                    f'File {output_path} already exists. Use --overwrite to replace it.'
                )
            )
            return
        
        try:
            if options['template']:
                # Generate empty template
                config_content = generate_config_template()
                self.stdout.write(
                    self.style.SUCCESS('Generating empty configuration template...')
                )
            else:
                # Export current configuration
                current_config = get_model_error_configs()
                config_content = json.dumps(current_config, indent=2, ensure_ascii=False)
                self.stdout.write(
                    self.style.SUCCESS('Exporting current configuration...')
                )
            
            # Write to file
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Configuration {"template" if options["template"] else "export"} '
                    f'saved to: {output_path}'
                )
            )
            
            # Show usage instructions
            self.stdout.write('\n' + '='*60)
            self.stdout.write('USAGE INSTRUCTIONS:')
            self.stdout.write('='*60)
            
            if options['template']:
                self.stdout.write(
                    '1. Edit the generated template file to add your model configurations'
                )
                self.stdout.write(
                    '2. Set API_ERROR_CONFIG_PATH in settings.py to point to your config file'
                )
                self.stdout.write(
                    '3. Restart your Django application to load the new configuration'
                )
            else:
                self.stdout.write(
                    '1. Edit the exported configuration file as needed'
                )
                self.stdout.write(
                    '2. Set API_ERROR_CONFIG_PATH in settings.py if not already set'
                )
                self.stdout.write(
                    '3. Restart your Django application to reload the configuration'
                )
            
            self.stdout.write('\nExample settings.py configuration:')
            self.stdout.write(f"API_ERROR_CONFIG_PATH = '{output_path}'")
            
            self.stdout.write('\nConfiguration structure:')
            self.stdout.write('''
{
  "YourModelViewSet": {
    "unique_constraints": {
      "field_name": {
        "message": "Custom error message",
        "error_code": "CUSTOM_ERROR_CODE"
      }
    },
    "model_name": "YourModel",
    "category": "your_category"
  }
}
            ''')
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error generating configuration: {e}')
            )
