"""
Django Ledger created by <PERSON> <m<PERSON><PERSON>@arrobalytics.com>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

API Error Handling Decorators for Django Ledger.
"""

import logging
from functools import wraps
from typing import Any, Dict, List, Optional, Union

from django.core.exceptions import ValidationError as DjangoValidationError
from django.db import IntegrityError
from rest_framework import status
from rest_framework.exceptions import ValidationError as DRFValidationError
from rest_framework.response import Response

logger = logging.getLogger(__name__)


class APIException(Exception):
    """Base API Exception class."""
    
    def __init__(self, message: str, error_code: str = "API_ERROR", status_code: int = 500):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(self.message)


class BusinessLogicError(APIException):
    """Exception for business logic errors."""
    
    def __init__(self, message: str, error_code: str = "BUSINESS_LOGIC_ERROR"):
        super().__init__(message, error_code, status.HTTP_400_BAD_REQUEST)


class ResourceNotFoundError(APIException):
    """Exception for resource not found errors."""
    
    def __init__(self, message: str, error_code: str = "RESOURCE_NOT_FOUND"):
        super().__init__(message, error_code, status.HTTP_404_NOT_FOUND)


class DuplicateResourceError(APIException):
    """Exception for duplicate resource errors."""
    
    def __init__(self, message: str, error_code: str = "DUPLICATE_RESOURCE"):
        super().__init__(message, error_code, status.HTTP_409_CONFLICT)


def format_error_response(
    error_message: str,
    error_code: str = "INTERNAL_ERROR",
    details: Optional[Dict[str, Any]] = None,
    status_code: int = 500
) -> Dict[str, Any]:
    """
    Format standardized error response.
    
    Parameters
    ----------
    error_message : str
        The error message
    error_code : str
        The error code
    details : Optional[Dict[str, Any]]
        Additional error details
    status_code : int
        HTTP status code
        
    Returns
    -------
    Dict[str, Any]
        Formatted error response
    """
    from datetime import datetime
    
    response = {
        "status": "error",
        "error_code": error_code,
        "message": error_message,
        "timestamp": datetime.now().isoformat()
    }
    
    if details:
        response["details"] = details
        
    return response


def api_exception_handler(func):
    """
    Decorator to handle API exceptions and return standardized error responses.
    
    This decorator catches common exceptions and formats them into consistent
    JSON error responses with appropriate HTTP status codes.
    
    Parameters
    ----------
    func : callable
        The view method to wrap
        
    Returns
    -------
    callable
        The wrapped function
    """
    @wraps(func)
    def wrapper(self, request, *args, **kwargs):
        try:
            return func(self, request, *args, **kwargs)
            
        except APIException as e:
            # Handle custom API exceptions
            logger.warning(f"API Exception in {func.__name__}: {e.message}")
            return Response(
                format_error_response(
                    error_message=e.message,
                    error_code=e.error_code,
                    status_code=e.status_code
                ),
                status=e.status_code
            )
            
        except DRFValidationError as e:
            # Handle DRF validation errors
            logger.warning(f"Validation Error in {func.__name__}: {str(e)}")
            details = {}
            if hasattr(e, 'detail') and isinstance(e.detail, dict):
                details = e.detail
            elif hasattr(e, 'detail') and isinstance(e.detail, list):
                details = {"non_field_errors": e.detail}
                
            return Response(
                format_error_response(
                    error_message="Validation failed",
                    error_code="VALIDATION_ERROR",
                    details=details,
                    status_code=status.HTTP_400_BAD_REQUEST
                ),
                status=status.HTTP_400_BAD_REQUEST
            )
            
        except DjangoValidationError as e:
            # Handle Django validation errors
            logger.warning(f"Django Validation Error in {func.__name__}: {str(e)}")
            details = {}
            if hasattr(e, 'message_dict'):
                details = e.message_dict
            elif hasattr(e, 'messages'):
                details = {"non_field_errors": e.messages}
                
            return Response(
                format_error_response(
                    error_message="Validation failed",
                    error_code="VALIDATION_ERROR",
                    details=details,
                    status_code=status.HTTP_400_BAD_REQUEST
                ),
                status=status.HTTP_400_BAD_REQUEST
            )
            
        except IntegrityError as e:
            # Handle database integrity errors
            logger.error(f"Integrity Error in {func.__name__}: {str(e)}")
            error_message = "Data integrity constraint violation"
            
            # Check for common integrity errors
            if "UNIQUE constraint failed" in str(e) or "duplicate key value" in str(e):
                error_message = "A record with this information already exists"
                error_code = "DUPLICATE_RESOURCE"
                status_code = status.HTTP_409_CONFLICT
            else:
                error_code = "INTEGRITY_ERROR"
                status_code = status.HTTP_400_BAD_REQUEST
                
            return Response(
                format_error_response(
                    error_message=error_message,
                    error_code=error_code,
                    status_code=status_code
                ),
                status=status_code
            )
            
        except ValueError as e:
            # Handle value errors (often from business logic)
            logger.warning(f"Value Error in {func.__name__}: {str(e)}")
            return Response(
                format_error_response(
                    error_message=str(e),
                    error_code="INVALID_VALUE",
                    status_code=status.HTTP_400_BAD_REQUEST
                ),
                status=status.HTTP_400_BAD_REQUEST
            )
            
        except PermissionError as e:
            # Handle permission errors
            logger.warning(f"Permission Error in {func.__name__}: {str(e)}")
            return Response(
                format_error_response(
                    error_message="You don't have permission to perform this action",
                    error_code="PERMISSION_DENIED",
                    status_code=status.HTTP_403_FORBIDDEN
                ),
                status=status.HTTP_403_FORBIDDEN
            )
            
        except Exception as e:
            # Handle unexpected errors
            logger.error(f"Unexpected Error in {func.__name__}: {str(e)}", exc_info=True)
            return Response(
                format_error_response(
                    error_message="An unexpected error occurred",
                    error_code="INTERNAL_ERROR",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                ),
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            
    return wrapper


def complex_model_exception_handler(
    exception_types: Optional[List[str]] = None,
    log_level: str = "warning"
):
    """
    Decorator specifically for complex models that are prone to errors.

    This decorator provides enhanced error handling for models with complex
    business logic, relationships, or validation rules.

    Parameters
    ----------
    exception_types : Optional[List[str]]
        List of specific exception types to handle
    log_level : str
        Logging level for errors (debug, info, warning, error, critical)

    Returns
    -------
    callable
        The decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            try:
                return func(self, request, *args, **kwargs)

            except APIException as e:
                # Handle custom API exceptions with enhanced logging
                getattr(logger, log_level)(
                    f"Complex Model API Exception in {func.__name__}: {e.message}",
                    extra={
                        'view_class': self.__class__.__name__,
                        'method': func.__name__,
                        'entity_slug': kwargs.get('entity_slug'),
                        'uuid': kwargs.get('uuid'),
                        'error_code': e.error_code
                    }
                )
                return Response(
                    format_error_response(
                        error_message=e.message,
                        error_code=e.error_code,
                        status_code=e.status_code
                    ),
                    status=e.status_code
                )

            except IntegrityError as e:
                # Enhanced handling for complex model integrity errors
                logger.error(
                    f"Complex Model Integrity Error in {func.__name__}: {str(e)}",
                    extra={
                        'view_class': self.__class__.__name__,
                        'method': func.__name__,
                        'entity_slug': kwargs.get('entity_slug'),
                        'uuid': kwargs.get('uuid'),
                        'raw_error': str(e)
                    }
                )

                # More specific error messages for complex models
                error_message = "Data integrity constraint violation"
                error_code = "INTEGRITY_ERROR"

                if "UNIQUE constraint failed" in str(e):
                    if "ma_bp_sd" in str(e):
                        error_message = "Department code already exists"
                        error_code = "DUPLICATE_DEPARTMENT_CODE"
                    elif "ten_bp_sd" in str(e):
                        error_message = "Department name already exists"
                        error_code = "DUPLICATE_DEPARTMENT_NAME"
                    else:
                        error_message = "A record with this information already exists"
                        error_code = "DUPLICATE_RESOURCE"

                return Response(
                    format_error_response(
                        error_message=error_message,
                        error_code=error_code,
                        status_code=status.HTTP_409_CONFLICT
                    ),
                    status=status.HTTP_409_CONFLICT
                )

            except Exception as e:
                # Enhanced error handling for complex models
                logger.error(
                    f"Complex Model Unexpected Error in {func.__name__}: {str(e)}",
                    exc_info=True,
                    extra={
                        'view_class': self.__class__.__name__,
                        'method': func.__name__,
                        'entity_slug': kwargs.get('entity_slug'),
                        'uuid': kwargs.get('uuid'),
                        'request_data': getattr(request, 'data', None)
                    }
                )
                return Response(
                    format_error_response(
                        error_message="An unexpected error occurred in complex model operation",
                        error_code="COMPLEX_MODEL_ERROR",
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
                    ),
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )

        return wrapper
    return decorator
