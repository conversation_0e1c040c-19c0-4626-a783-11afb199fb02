#!/usr/bin/env python
"""
Simple test for API Error Handling Decorator.
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'x_erp.settings')
django.setup()

from django.test import TestCase, RequestFactory
from django.contrib.auth.models import User
from rest_framework.test import APITestCase, APIClient
from rest_framework.authtoken.models import Token
from django_ledger.models import EntityModel
from django_ledger.api.views.danh_muc.tai_san_va_cong_cu.bo_phan_su_dung_ccdc import BoPhanSuDungCCDCViewSet
from django_ledger.api.decorators import api_exception_handler, complex_model_exception_handler


def test_decorator_functionality():
    """Test decorator functionality directly."""
    print("🧪 Testing Decorator Functionality")
    
    # Test 1: Test api_exception_handler with ValueError
    @api_exception_handler
    def test_view_with_value_error(self, request, *args, **kwargs):
        raise ValueError("Test value error")
    
    # Create mock request
    factory = RequestFactory()
    request = factory.get('/')
    
    # Create mock view instance
    class MockView:
        pass
    
    view = MockView()
    
    # Test the decorator
    try:
        response = test_view_with_value_error(view, request)
        print(f"✅ api_exception_handler caught ValueError: {response.status_code}")
        print(f"   Response data: {response.data}")
    except Exception as e:
        print(f"❌ api_exception_handler failed: {e}")
    
    # Test 2: Test complex_model_exception_handler
    @complex_model_exception_handler(log_level="info")
    def test_view_with_integrity_error(self, request, *args, **kwargs):
        from django.db import IntegrityError
        raise IntegrityError("UNIQUE constraint failed: ma_bp_sd")
    
    try:
        response = test_view_with_integrity_error(view, request, entity_slug="test", uuid="test-uuid")
        print(f"✅ complex_model_exception_handler caught IntegrityError: {response.status_code}")
        print(f"   Response data: {response.data}")
    except Exception as e:
        print(f"❌ complex_model_exception_handler failed: {e}")
    
    # Test 3: Test normal execution (no error)
    @api_exception_handler
    def test_view_normal(self, request, *args, **kwargs):
        from rest_framework.response import Response
        return Response({"message": "success"})
    
    try:
        response = test_view_normal(view, request)
        print(f"✅ Normal execution works: {response.status_code}")
        print(f"   Response data: {response.data}")
    except Exception as e:
        print(f"❌ Normal execution failed: {e}")


def test_with_real_viewset():
    """Test with real ViewSet using Django test client."""
    print("\n🧪 Testing with Real ViewSet")
    
    try:
        # Create test client
        client = APIClient()
        
        # Create test user and entity
        user = User.objects.create_user(
            username='testuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create token
        token = Token.objects.create(user=user)
        
        # Get or create entity
        entity, created = EntityModel.objects.get_or_create(
            slug='test-entity-decorator',
            defaults={
                'name': 'Test Entity for Decorator',
                'admin': user
            }
        )
        
        # Set authentication
        client.credentials(HTTP_AUTHORIZATION=f'Token {token.key}')
        
        # Test data
        test_data = {
            "ma_bp_sd": "TEST001",
            "ten_bp_sd": "Test Department",
            "mo_ta": "Test description",
            "status": "1"
        }
        
        # Test 1: Valid creation
        print("   Testing valid creation...")
        url = f'/api/entities/{entity.slug}/erp/danh-muc/tai-san-va-cong-cu/bo-phan-su-dung-ccdc/'
        response = client.post(url, test_data, format='json')
        print(f"   Status: {response.status_code}")
        if response.status_code == 201:
            print("   ✅ Valid creation successful")
            created_uuid = response.data.get('uuid')
        else:
            print(f"   ❌ Valid creation failed: {response.data}")
            created_uuid = None
        
        # Test 2: Duplicate creation (should trigger decorator)
        print("   Testing duplicate creation...")
        response = client.post(url, test_data, format='json')
        print(f"   Status: {response.status_code}")
        if response.status_code in [400, 409]:
            print("   ✅ Duplicate creation properly handled")
            print(f"   Response: {response.data}")
            
            # Check if our decorator format is used
            if 'error_code' in response.data and 'timestamp' in response.data:
                print("   ✅ Decorator error format detected!")
            else:
                print("   ⚠️  Standard DRF error format (decorator not triggered)")
        else:
            print(f"   ❌ Duplicate creation not handled: {response.data}")
        
        # Test 3: Invalid data
        print("   Testing invalid data...")
        invalid_data = {"ma_bp_sd": "", "ten_bp_sd": ""}
        response = client.post(url, invalid_data, format='json')
        print(f"   Status: {response.status_code}")
        if response.status_code == 400:
            print("   ✅ Invalid data properly handled")
            print(f"   Response: {response.data}")
        else:
            print(f"   ❌ Invalid data not handled: {response.data}")
        
        # Test 4: Update non-existent record
        print("   Testing update non-existent record...")
        fake_uuid = "00000000-0000-0000-0000-000000000000"
        update_url = f'{url}{fake_uuid}/'
        response = client.put(update_url, test_data, format='json')
        print(f"   Status: {response.status_code}")
        if response.status_code == 404:
            print("   ✅ Non-existent record properly handled")
        else:
            print(f"   ❌ Non-existent record not handled: {response.data}")
        
        # Cleanup
        if created_uuid:
            client.delete(f'{url}{created_uuid}/')
        
        entity.delete()
        user.delete()
        
    except Exception as e:
        print(f"   ❌ Real ViewSet test failed: {e}")
        import traceback
        traceback.print_exc()


def test_error_response_format():
    """Test error response format."""
    print("\n🧪 Testing Error Response Format")
    
    from django_ledger.api.decorators.error_handling import format_error_response
    
    # Test format_error_response function
    error_response = format_error_response(
        error_message="Test error message",
        error_code="TEST_ERROR",
        details={"field": "test_field"},
        status_code=400
    )
    
    print("   Testing format_error_response function:")
    required_fields = ["status", "error_code", "message", "timestamp"]
    for field in required_fields:
        if field in error_response:
            print(f"   ✅ {field}: {error_response[field]}")
        else:
            print(f"   ❌ Missing field: {field}")
    
    if error_response.get("status") == "error":
        print("   ✅ Status field is 'error'")
    else:
        print(f"   ❌ Status field should be 'error', got: {error_response.get('status')}")


if __name__ == "__main__":
    print("🎯 Simple API Error Handling Decorator Test")
    print("=" * 60)
    
    try:
        test_decorator_functionality()
        test_with_real_viewset()
        test_error_response_format()
        
        print("\n" + "=" * 60)
        print("🏁 All tests completed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n💥 Test failed with error: {e}")
        import traceback
        traceback.print_exc()
