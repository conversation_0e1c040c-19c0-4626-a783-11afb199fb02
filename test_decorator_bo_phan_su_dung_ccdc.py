#!/usr/bin/env python
"""
Test script for API Error Handling Decorator with BoPhanSuDungCCDC model.
"""

import json
import requests
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
ENTITY_SLUG = "test-entity"  # Replace with actual entity slug
API_TOKEN = None  # Replace with actual token if needed

# Test data
TEST_DATA = {
    "valid_data": {
        "ma_bp_sd": "BP001",
        "ten_bp_sd": "Phòng Kỹ Thuật",
        "mo_ta": "Phòng kỹ thuật chịu trách nhiệm về công cụ dụng cụ",
        "status": "1"
    },
    "duplicate_data": {
        "ma_bp_sd": "BP001",  # Same code to trigger duplicate error
        "ten_bp_sd": "Phòng Kỹ Thuật Khác",
        "mo_ta": "Duplicate test",
        "status": "1"
    },
    "invalid_data": {
        "ma_bp_sd": "",  # Empty required field
        "ten_bp_sd": "",
        "status": "invalid_status"
    }
}

def make_request(method, endpoint, data=None, headers=None):
    """Make HTTP request and return response."""
    url = f"{BASE_URL}{endpoint}"
    
    default_headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    if API_TOKEN:
        default_headers["Authorization"] = f"Token {API_TOKEN}"
    
    if headers:
        default_headers.update(headers)
    
    try:
        if method.upper() == "GET":
            response = requests.get(url, headers=default_headers)
        elif method.upper() == "POST":
            response = requests.post(url, json=data, headers=default_headers)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=default_headers)
        elif method.upper() == "DELETE":
            response = requests.delete(url, headers=default_headers)
        else:
            raise ValueError(f"Unsupported method: {method}")
            
        return response
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return None

def print_test_result(test_name, response, expected_status=None):
    """Print formatted test result."""
    print(f"\n{'='*60}")
    print(f"TEST: {test_name}")
    print(f"{'='*60}")
    
    if response is None:
        print("❌ Request failed - No response")
        return False
    
    print(f"Status Code: {response.status_code}")
    
    try:
        response_data = response.json()
        print(f"Response: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
    except:
        print(f"Response Text: {response.text}")
    
    if expected_status and response.status_code == expected_status:
        print(f"✅ Expected status {expected_status} - PASSED")
        return True
    elif expected_status:
        print(f"❌ Expected status {expected_status}, got {response.status_code} - FAILED")
        return False
    else:
        print("ℹ️  Status check skipped")
        return True

def test_decorator_functionality():
    """Test the decorator functionality with various scenarios."""
    print(f"🚀 Starting API Error Handling Decorator Tests")
    print(f"Time: {datetime.now().isoformat()}")
    print(f"Base URL: {BASE_URL}")
    print(f"Entity Slug: {ENTITY_SLUG}")
    
    endpoint = f"/api/entities/{ENTITY_SLUG}/erp/danh-muc/tai-san-va-cong-cu/bo-phan-su-dung-ccdc/"
    created_uuid = None
    
    # Test 1: Valid creation (should work)
    print(f"\n🧪 Test 1: Valid Data Creation")
    response = make_request("POST", endpoint, TEST_DATA["valid_data"])
    success = print_test_result("Create with valid data", response, 201)
    
    if success and response and response.status_code == 201:
        try:
            created_uuid = response.json().get("uuid")
            print(f"✅ Created record with UUID: {created_uuid}")
        except:
            pass
    
    # Test 2: Duplicate creation (should trigger decorator)
    print(f"\n🧪 Test 2: Duplicate Data Creation (Testing Decorator)")
    response = make_request("POST", endpoint, TEST_DATA["duplicate_data"])
    print_test_result("Create with duplicate data", response, 409)
    
    # Test 3: Invalid data (should trigger validation error)
    print(f"\n🧪 Test 3: Invalid Data Creation (Testing Validation)")
    response = make_request("POST", endpoint, TEST_DATA["invalid_data"])
    print_test_result("Create with invalid data", response, 400)
    
    # Test 4: Update non-existent record (should trigger not found)
    print(f"\n🧪 Test 4: Update Non-existent Record")
    fake_uuid = "00000000-0000-0000-0000-000000000000"
    update_endpoint = f"{endpoint}{fake_uuid}/"
    response = make_request("PUT", update_endpoint, TEST_DATA["valid_data"])
    print_test_result("Update non-existent record", response, 404)
    
    # Test 5: Delete non-existent record
    print(f"\n🧪 Test 5: Delete Non-existent Record")
    response = make_request("DELETE", update_endpoint)
    print_test_result("Delete non-existent record", response, 404)
    
    # Test 6: Valid update (if we have a created record)
    if created_uuid:
        print(f"\n🧪 Test 6: Valid Update")
        update_endpoint = f"{endpoint}{created_uuid}/"
        update_data = {
            "ten_bp_sd": "Phòng Kỹ Thuật Cập Nhật",
            "mo_ta": "Mô tả đã được cập nhật"
        }
        response = make_request("PUT", update_endpoint, update_data)
        print_test_result("Update existing record", response, 200)
        
        # Test 7: Valid delete
        print(f"\n🧪 Test 7: Valid Delete")
        response = make_request("DELETE", update_endpoint)
        print_test_result("Delete existing record", response, 204)
    
    # Test 8: List records
    print(f"\n🧪 Test 8: List Records")
    response = make_request("GET", endpoint)
    print_test_result("List records", response, 200)
    
    print(f"\n{'='*60}")
    print(f"🏁 Tests completed!")
    print(f"{'='*60}")

def test_error_response_format():
    """Test that error responses follow the expected format."""
    print(f"\n🔍 Testing Error Response Format")
    
    endpoint = f"/api/entities/{ENTITY_SLUG}/erp/danh-muc/tai-san-va-cong-cu/bo-phan-su-dung-ccdc/"
    
    # Create a record first
    response = make_request("POST", endpoint, TEST_DATA["valid_data"])
    
    # Try to create duplicate to get error response
    response = make_request("POST", endpoint, TEST_DATA["duplicate_data"])
    
    if response and response.status_code in [400, 409]:
        try:
            error_data = response.json()
            print(f"\n📋 Error Response Format Check:")
            
            required_fields = ["status", "error_code", "message", "timestamp"]
            for field in required_fields:
                if field in error_data:
                    print(f"✅ {field}: {error_data[field]}")
                else:
                    print(f"❌ Missing field: {field}")
            
            if error_data.get("status") == "error":
                print("✅ Status field is 'error'")
            else:
                print(f"❌ Status field should be 'error', got: {error_data.get('status')}")
                
        except Exception as e:
            print(f"❌ Failed to parse error response: {e}")
    else:
        print("❌ Could not get error response for format testing")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        ENTITY_SLUG = sys.argv[1]
    
    if len(sys.argv) > 2:
        API_TOKEN = sys.argv[2]
    
    print("🎯 API Error Handling Decorator Test Suite")
    print("=" * 60)
    
    try:
        test_decorator_functionality()
        test_error_response_format()
    except KeyboardInterrupt:
        print("\n\n⏹️  Tests interrupted by user")
    except Exception as e:
        print(f"\n\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
