# Simple test for ma_tg_ts validation
from django.test import RequestFactory
from rest_framework.exceptions import ValidationError
from django_ledger.models import EntityModel
from django_ledger.models.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh import LyDoTangGiamTaiSanCoDinhModel
from django_ledger.api.serializers.danh_muc.tai_san_va_cong_cu.ly_do_tang_giam_tai_san_co_dinh import LyDoTangGiamTaiSanCoDinhSerializer

print("🧪 Testing ma_tg_ts validation...")

# Get first entity
entity = EntityModel.objects.first()
if not entity:
    print("❌ No entity found")
    exit()

print(f"✅ Using entity: {entity.slug}")
entity_slug = entity.slug

# Create test data
factory = RequestFactory()
test_ma_tg_ts = "TEST_MA_001"

# Create or get test record
test_record, created = LyDoTangGiamTaiSanCoDinhModel.objects.get_or_create(
    entity_model=entity,
    ma_tg_ts=test_ma_tg_ts,
    defaults={
        'loai_tg_ts': 'Test Type',
        'ten_tg_ts': 'Test Name',
        'ten_tg_ts2': 'Test Name 2',
        'status': '1'
    }
)

if created:
    print(f"✅ Created test record with ma_tg_ts: {test_ma_tg_ts}")
else:
    print(f"✅ Using existing test record with ma_tg_ts: {test_ma_tg_ts}")

# Test Case 1: Try to create duplicate ma_tg_ts (should fail)
print("\n📝 Test Case 1: Creating duplicate ma_tg_ts (should fail)")

request = factory.post(f'/api/entities/{entity_slug}/erp/danh-muc/tai-san-va-cong-cu/ly-do-tang-giam-tai-san-co-dinh/')
request.parser_context = {'kwargs': {'entity_slug': entity_slug}}

serializer_data = {
    'loai_tg_ts': 'Another Type',
    'ma_tg_ts': test_ma_tg_ts,  # Same ma_tg_ts - should fail
    'ten_tg_ts': 'Another Name',
    'ten_tg_ts2': 'Another Name 2',
    'status': '1'
}

serializer = LyDoTangGiamTaiSanCoDinhSerializer(
    data=serializer_data,
    context={'request': request}
)

try:
    serializer.is_valid(raise_exception=True)
    print("❌ FAILED: Validation should have failed for duplicate ma_tg_ts")
except ValidationError as e:
    if 'ma_tg_ts' in str(e):
        print("✅ PASSED: Validation correctly rejected duplicate ma_tg_ts")
        print(f"   Error message: {e}")
    else:
        print(f"❌ FAILED: Unexpected validation error: {e}")

# Test Case 2: Try to create unique ma_tg_ts (should pass)
print("\n📝 Test Case 2: Creating unique ma_tg_ts (should pass)")

unique_ma_tg_ts = "TEST_MA_002"
serializer_data['ma_tg_ts'] = unique_ma_tg_ts

serializer = LyDoTangGiamTaiSanCoDinhSerializer(
    data=serializer_data,
    context={'request': request}
)

try:
    serializer.is_valid(raise_exception=True)
    print("✅ PASSED: Validation correctly accepted unique ma_tg_ts")
except ValidationError as e:
    print(f"❌ FAILED: Validation should have passed for unique ma_tg_ts: {e}")

print("\n🎉 Test completed!")
