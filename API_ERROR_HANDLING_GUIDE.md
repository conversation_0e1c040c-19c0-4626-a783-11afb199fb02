# API Error Handling Decorator Guide

## 🎯 Overview

This guide explains how to use the configuration-driven API error handling decorators in Django Ledger. The system provides standardized error responses and intelligent error detection for complex ERP models.

## ✨ Features

- **Configuration-driven**: No hardcoded if-else chains for 100+ models
- **Standardized responses**: Consistent JSON error format across all APIs
- **Smart error detection**: Automatic parsing of database constraint violations
- **Enhanced logging**: Structured logging with context information
- **External configuration**: JSON-based configuration for easy maintenance
- **Dynamic updates**: Runtime configuration updates without code changes

## 🚀 Quick Start

### 1. Apply Decorators to Your ViewSet

```python
from django_ledger.api.decorators import (
    api_exception_handler,
    complex_model_exception_handler
)

class YourModelViewSet(EntityRelatedViewSet):
    
    @complex_model_exception_handler(log_level="info")
    def create(self, request, *args, **kwargs):
        # Your create logic
        pass
    
    @complex_model_exception_handler(log_level="info") 
    def update(self, request, *args, **kwargs):
        # Your update logic
        pass
    
    @api_exception_handler
    def destroy(self, request, *args, **kwargs):
        # Your delete logic
        pass
```

### 2. Configure Error Messages

Generate configuration file:
```bash
python manage.py generate_error_config --output api_error_config.json
```

Edit the configuration:
```json
{
  "YourModelViewSet": {
    "unique_constraints": {
      "ma_field": {
        "message": "Field code already exists",
        "error_code": "DUPLICATE_FIELD_CODE"
      },
      "email": {
        "message": "Email address already exists", 
        "error_code": "DUPLICATE_EMAIL"
      }
    },
    "model_name": "YourModel",
    "category": "your_category"
  }
}
```

### 3. Update Settings

Add to your `settings.py`:
```python
API_ERROR_CONFIG_PATH = os.path.join(BASE_DIR, 'api_error_config.json')
```

## 📋 Available Decorators

### `@api_exception_handler`
Basic decorator for standard error handling.

**Use for:**
- Simple CRUD operations
- Delete operations
- Read-only operations

**Handles:**
- ValidationError (DRF & Django)
- IntegrityError
- ValueError
- PermissionError
- Generic exceptions

### `@complex_model_exception_handler`
Advanced decorator for complex models with enhanced logging.

**Use for:**
- Complex business logic
- Models with many relationships
- Create/Update operations
- Models prone to constraint violations

**Additional features:**
- Enhanced structured logging
- Model-specific error detection
- Configurable log levels
- Request data logging

**Parameters:**
- `log_level`: "debug", "info", "warning", "error", "critical"

## 🔧 Configuration System

### Default Configuration

The system includes pre-configured error handling for common ERP models:

- **Department models**: BoPhanSuDungCCDCViewSet, BoPhanSuDungTSViewSet
- **Customer models**: CustomerViewSet
- **Vendor models**: VendorViewSet  
- **Material models**: VatTuViewSet
- **Account models**: AccountModelViewSet
- **Warehouse models**: KhoHangViewSet
- **Employee models**: NhanVienModelViewSet
- **Document models**: InvoiceViewSet, BillViewSet

### External Configuration

Create external JSON file for custom configurations:

```json
{
  "CustomModelViewSet": {
    "unique_constraints": {
      "custom_field": {
        "message": "Custom field already exists",
        "error_code": "DUPLICATE_CUSTOM_FIELD"
      }
    },
    "model_name": "CustomModel",
    "category": "custom"
  }
}
```

### Dynamic Configuration

Add configurations at runtime:

```python
from django_ledger.api.decorators.error_config import add_model_config

config = {
    'unique_constraints': {
        'field_name': {
            'message': 'Field already exists',
            'error_code': 'DUPLICATE_FIELD'
        }
    },
    'model_name': 'DynamicModel',
    'category': 'dynamic'
}

add_model_config("DynamicModelViewSet", config)
```

## 📊 Error Response Format

All errors return standardized JSON responses:

```json
{
  "status": "error",
  "error_code": "DUPLICATE_DEPARTMENT_CODE", 
  "message": "Department code already exists",
  "timestamp": "2025-06-11T10:41:46.402861",
  "details": {
    "field": "ma_bp_sd",
    "constraint": "unique"
  }
}
```

### Error Codes

| Type | Code | Description |
|------|------|-------------|
| Validation | `VALIDATION_ERROR` | Field validation failed |
| Duplicate | `DUPLICATE_*_CODE` | Unique constraint violation |
| Missing | `MISSING_REQUIRED_FIELD` | Required field is null |
| Reference | `INVALID_REFERENCE` | Foreign key constraint |
| Permission | `PERMISSION_DENIED` | Access denied |
| Generic | `INTERNAL_ERROR` | Unexpected error |

## 🛠️ Management Commands

### Generate Configuration Template

```bash
# Generate empty template
python manage.py generate_error_config --template

# Export current configuration  
python manage.py generate_error_config --output current_config.json

# Overwrite existing file
python manage.py generate_error_config --overwrite
```

## 📈 Benefits for ERP Systems

### For 20+ Years ERP Experience

1. **Scalability**: Handle 100+ models without code duplication
2. **Maintainability**: Centralized error configuration
3. **Consistency**: Standardized error responses across all modules
4. **Flexibility**: Easy customization per business requirements
5. **Debugging**: Enhanced logging with business context
6. **Performance**: Minimal overhead with intelligent caching

### Business Value

- **Reduced Development Time**: No manual error handling per model
- **Better User Experience**: Clear, consistent error messages
- **Easier Maintenance**: Configuration-driven approach
- **Faster Debugging**: Structured logging with context
- **Compliance Ready**: Standardized audit trails

## 🔍 Testing

Run the test suite to verify functionality:

```bash
python test_decorator_simple.py
```

The test covers:
- Basic decorator functionality
- Configuration system
- Smart error detection
- Response format validation
- Dynamic configuration

## 📝 Best Practices

1. **Use appropriate decorators**: `@complex_model_exception_handler` for complex models, `@api_exception_handler` for simple operations

2. **Configure error messages**: Provide business-friendly error messages in configuration

3. **Set appropriate log levels**: Use "info" for business operations, "error" for system issues

4. **External configuration**: Use JSON files for production environments

5. **Test thoroughly**: Verify error handling with various constraint violations

6. **Monitor logs**: Use structured logging for debugging and monitoring

## 🚨 Migration from Hardcoded Approach

If you have existing hardcoded error handling:

1. Remove manual try-catch blocks
2. Apply appropriate decorators
3. Add model configuration
4. Test error scenarios
5. Update frontend error handling if needed

The decorator system will handle all error scenarios automatically while providing better error messages and logging.
